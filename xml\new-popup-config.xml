<?xml version="1.0" encoding="utf-8"?>
<config>
	<popup id="bunrui1Search" title="大分類検索" width="750" height="650" init="1" page="0" description="大分類検索">
		<panel width="300" cols="2">
			<message id="" value="大分類名" order="1"  />
			<input id="term_bunrui1_na" type="text" maxlength="20" order="2" value="term_bunrui1_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="3" style="" />
			<input id="param2" type="text" maxlength="8" order="4" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="5" value="大分類名はあいまい検索となります。" />
			<input id="search_button" type="button" order="6" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="7" source=""/>
			<input id="param1" type="hidden" value="param1" order="8" source=""/>
			<input id="param3" type="hidden" value="param3" order="9" source=""/>
			<input id="param4" type="hidden" value="param4" order="10" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="bunrui1_cd" mapping="BUNRUI1_CD" label="大分類コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui1_kanji_na" mapping="BUNRUI1_KANJI_NA" label="大分類名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui1SearchQuery</id>
			<body>
				SELECT DISTINCT
					RB1.BUNRUI1_CD,
					RB1.BUNRUI1_KANJI_NA
				FROM
					R_BUNRUI1 RB1
					INNER JOIN R_BUNRUI2 RB2
						ON RB2.BUNRUI1_CD = RB1.BUNRUI1_CD
					INNER JOIN R_BUNRUI3 RB3
						ON RB3.BUNRUI2_CD = RB2.BUNRUI2_CD
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'BUNRUI1'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="param4">RB1.SYSTEM_KB=:param4</where>
			<where order="3" bind="term_bunrui1_na">RB1.BUNRUI1_KANJI_NA LIKE %:term_bunrui1_na%</where>
			<orderby>RB1.BUNRUI1_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="bunrui2Search" title="中分類検索" width="750" height="650" init="1" page="0" description="中分類検索">
		<panel width="520" cols="2">
			<message id="" value="*大分類コード" order="1"  />
			<input id="param5" type="text" maxlength="2" order="2" value="param5"  readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="中分類名" order="3"  />
			<input id="term_bunrui2_na" type="text" maxlength="20" order="4" value="term_bunrui2_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="中分類名はあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
			<input id="param4" type="hidden" value="param4" order="12" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="bunrui2_cd" mapping="BUNRUI2_CD" label="中分類コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui2_kanji_na" mapping="BUNRUI2_KANJI_NA" label="中分類名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui2SearchQuery</id>
			<body>
				SELECT DISTINCT
					RB2.BUNRUI2_CD,
					RB2.BUNRUI2_KANJI_NA
				FROM
					R_BUNRUI2 RB2
					INNER JOIN R_BUNRUI3 RB3
						ON RB3.BUNRUI2_CD = RB2.BUNRUI2_CD
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'BUNRUI2'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="param4">RB2.SYSTEM_KB=:param4</where>
			<where order="3" bind="param5">RTRIM(RB2.BUNRUI1_CD)=:param5</where>
			<where order="4" bind="term_bunrui2_na">RB2.BUNRUI2_KANJI_NA LIKE %:term_bunrui2_na%</where>
			<orderby>RB2.BUNRUI2_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="bunrui3Search" title="中小分類検索" width="750" height="650" init="1" page="0" description="中小分類検索">
		<panel width="520" cols="2">
			<message id="" value="*大分類コード" order="1"  />
			<input id="param5" type="text" maxlength="2" order="2" value="param5"  readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="*中分類コード" order="3"  />
			<input id="param6" type="text" maxlength="4" order="4" value="param6" readonly="true">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="中小分類名" order="5"  />
			<input id="term_bunrui3_na" type="text" maxlength="20" order="6" value="term_bunrui3_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="7" style="" />
			<input id="param2" type="text" maxlength="8" order="8" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="9" value="中小分類名はあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="10" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="11" source=""/>
			<input id="param1" type="hidden" value="param1" order="12" source=""/>
			<input id="param3" type="hidden" value="param3" order="13" source=""/>
			<input id="param4" type="hidden" value="param4" order="14" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="bunrui3_cd" mapping="BUNRUI3_CD" label="中小分類コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui3_kanji_na" mapping="BUNRUI3_KANJI_NA" label="中小分類名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui3SearchQuery</id>
			<body>
				SELECT DISTINCT
					RB3.BUNRUI3_CD,
					RB3.BUNRUI3_KANJI_NA
				FROM
					R_BUNRUI3 RB3
					INNER JOIN R_BUNRUI2 RB2
						ON RB2.BUNRUI2_CD = RB3.BUNRUI2_CD
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'BUNRUI3'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="param4">RB3.SYSTEM_KB=:param4</where>
			<where order="3" bind="param5">RTRIM(RB2.BUNRUI1_CD) = :param5</where>
			<where order="4" bind="param6">RTRIM(RB3.BUNRUI2_CD) = :param6</where>
			<where order="5" bind="term_bunrui3_na">RB3.BUNRUI3_KANJI_NA LIKE %:term_bunrui3_na%</where>
			<orderby>RB3.BUNRUI3_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="bunrui4Search" title="小分類検索" width="750" height="700" init="1" page="0" description="小分類検索">
		<panel width="520" cols="2">
			<message id="" value="*大分類コード" order="1"  />
			<input id="param5" type="text" maxlength="2" order="2" value="param5"  readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="*中分類コード" order="3"  />
			<input id="param6" type="text" maxlength="4" order="4" value="param6"
				   readonly="true">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="*中小分類コード" order="5"  />
			<input id="param7" type="text" maxlength="6" order="6" value="param7"
				   readonly="true">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="6" alert="最大入力文字数は6文字です。" />
			</input>
			<message id="" value="小分類名" order="7"  />
			<input id="term_bunrui4_na" type="text" maxlength="20" order="8" value="term_bunrui4_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="9" style="" />
			<input id="param2" type="text" maxlength="8" order="10" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="11" value="小分類名はあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="12" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="13" source=""/>
			<input id="param1" type="hidden" value="param1" order="14" source=""/>
			<input id="param3" type="hidden" value="param3" order="15" source=""/>
			<input id="param4" type="hidden" value="param4" order="16" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="bunrui4_cd" mapping="BUNRUI4_CD" label="小分類コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui4_kanji_na" mapping="BUNRUI4_KANJI_NA" label="小分類名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui4SearchQuery</id>
			<body>
				SELECT DISTINCT
					RB4.BUNRUI4_CD,
					RB4.BUNRUI4_KANJI_NA
				FROM
					R_BUNRUI4 RB4
					INNER JOIN R_BUNRUI3 RB3
						ON RB3.BUNRUI3_CD = RB4.BUNRUI3_CD
					INNER JOIN R_BUNRUI2 RB2
						ON RB2.BUNRUI2_CD = RB3.BUNRUI2_CD
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'BUNRUI4'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="param4">RB4.SYSTEM_KB=:param4</where>
			<where order="3" bind="param5">RTRIM(RB2.BUNRUI1_CD)=:param5</where>
			<where order="4" bind="param6">RTRIM(RB3.BUNRUI2_CD)=:param6</where>
			<where order="5" bind="param7">RTRIM(RB4.BUNRUI3_CD)=:param7</where>
			<where order="6" bind="term_bunrui4_na">RB4.BUNRUI4_KANJI_NA LIKE %:term_bunrui4_na%</where>
			<orderby>RB4.BUNRUI4_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="bunrui5Search" title="分類５検索" width="750" height="700" init="1" page="0" description="分類５検索">
		<panel width="520" cols="2">
			<message id="" value="*大分類コード" order="1"  />
			<input id="param5" type="text" maxlength="2" order="2" value="param5" style="" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="*中分類コード" order="3"  />
			<input id="param6" type="text" maxlength="4" order="4" value="param6" style="" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="*中小分類コード" order="5"  />
			<input id="param7" type="text" maxlength="6" order="6" value="param7" style="" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="6" alert="最大入力文字数は6文字です。" />
			</input>
			<message id="" value="*小分類コード" order="7"  />
			<input id="param8" type="text" maxlength="8" order="8" value="param8" style="" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="8" alert="最大入力文字数は8文字です。" />
			</input>
			<message id="" value="分類５名" order="9"  />
			<input id="term_bunrui5_na" type="text" maxlength="20" order="10" value="term_bunrui5_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="11" style="" />
			<input id="param2" type="text" maxlength="8" order="12" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="13" value="分類５名はあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="14" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="15" source=""/>
			<input id="param1" type="hidden" value="param1" order="16" source=""/>
			<input id="param3" type="hidden" value="param3" order="17" source=""/>
			<input id="param4" type="hidden" value="param4" order="18" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="bunrui5_cd" mapping="BUNRUI5_CD" label="分類５コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui5_kanji_na" mapping="BUNRUI5_KANJI_NA" label="分類５名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui5SearchQuery</id>
			<body>
				SELECT DISTINCT
					RB5.BUNRUI5_CD,
					RB5.BUNRUI5_KANJI_NA
				FROM
					R_BUNRUI5 RB5
					INNER JOIN R_BUNRUI4 RB4
						ON RB4.BUNRUI4_CD = RB5.BUNRUI4_CD
					INNER JOIN R_BUNRUI3 RB3
						ON RB3.BUNRUI3_CD = RB4.BUNRUI3_CD
					INNER JOIN R_BUNRUI2 RB2
						ON RB2.BUNRUI2_CD = RB3.BUNRUI2_CD
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'BUNRUI5'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="1" bind="param4">RB5.SYSTEM_KB=:param4</where>
			<where order="2" bind="param5">RTRIM(RB2.BUNRUI1_CD)=:param5</where>
			<where order="3" bind="param6">RTRIM(RB3.BUNRUI2_CD)=:param6</where>
			<where order="4" bind="param7">RTRIM(RB4.BUNRUI3_CD)=:param7</where>
			<where order="5" bind="param8">RTRIM(RB5.BUNRUI4_CD)=:param8</where>
			<where order="6" bind="term_bunrui5_na">RB5.BUNRUI5_KANJI_NA LIKE %:term_bunrui5_na%</where>
			<orderby>RB5.BUNRUI5_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="syohinSearch" title="商品検索" width="750" height="820" init="0" page="100" description="商品検索(分類1指定)">
		<panel width="520" cols="2">
			<message id="" value="*大分類コード" order="1" script="onblur='bunruiOnblur(this,1)'"/>
			<input id="param4" type="text" maxlength="2" order="2" value="param4" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="中分類コード" order="3" />
			<input id="param5" type="text" maxlength="4" order="4" value="param5" script="onblur='bunruiOnblur(this,2)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="中小分類コード" order="5" />
			<input id="param6" type="text" maxlength="6" order="6" value="param6" script="onblur='bunruiOnblur(this,3)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="6" alert="最大入力文字数は6文字です。" />
			</input>
			<message id="" value="小分類コード" order="7" />
			<input id="param7" type="text" maxlength="9" order="8" value="param7" script="onblur='bunruiOnblur(this,4)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="9" alert="最大入力文字数は9文字です。" />
			</input>
			<message order="9" id="" value="商品コード" />
			<input order="10" id="term_syohin_cd" value="term_syohin_cd" type="text" maxlength="13" script="onblur='syohinOnblur(this);'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="13" alert="最大入力文字数は13文字です。" />
			</input>
			<message order="11" id="" value="商品名" />
			<input order="12" id="term_syohin_na" value="term_syohin_na" type="text" maxlength="20">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="13" style="" />
			<input id="param2" type="text" maxlength="8" order="14" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message order="15" id="notice1" value="商品コードまたは商品名の一部を入力して検索ボタンを押してください。" style="text-align:left;" />
			<message order="16" id="notice2" value="商品コード、商品名を入力した場合はあいまい検索となります。" style="text-align:left;" />
			<message order="17" id="notice3" value="商品数が多い場合、検索及び改ページに時間がかかることがあります。" style="text-align:left;" />
			<input order="18" id="search_button" value="検索" type="button" script="doSearch()" style="text-align:center;" />
			<input id="param0" type="hidden" value="param0" order="19" source=""/>
			<input id="param1" type="hidden" value="param1" order="20" source=""/>
			<input id="param3" type="hidden" value="param3" order="21" source=""/>
			<input id="param8" type="hidden" value="param8" order="22" source=""/>
		</panel>

		<list width="570" cols="3">
			<column order="1" name="syohin_cd"          mapping="SYOHIN_CD" label="商品コード" width="170" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column order="2" name="hinmei_kanji_na"    mapping="HINMEI_KANJI_NA" label="商品名" width="310" style="text-align:left;padding: 0px 5px 0 5px" />
			<column order="3" name="Select" label="" width="70" style="text-align:center;" />
		</list>
		<sql>
			<id>SyohinSearchQuery</id>
			<body>
				SELECT
					SYOHIN_CD,
					TRIM(CONCAT(KIKAKU_KANJI_2_NA, ' ', SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA)) HINMEI_KANJI_NA
				FROM R_SYOHIN
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
					    ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'SYOHIN'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN YUKO_DT AND YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="param4">TRIM(BUNRUI1_CD)=:param4</where>
			<where order="4" bind="param5">TRIM(BUNRUI2_CD)=:param5</where>
			<where order="5" bind="param6">TRIM(BUNRUI3_CD)=:param6</where>
			<where order="6" bind="param7">TRIM(BUNRUI4_CD)=:param7</where>
			<where order="7" bind="param8">SHIIRE_HANBAI_KB=:param8</where>
			<where order="8" bind="term_syohin_cd">SYOHIN_CD LIKE %:term_syohin_cd%</where>
			<where order="9" bind="term_syohin_na">SYOHIN_KANJI_NA LIKE %:term_syohin_na%</where>
			<orderby>SYOHIN_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="syohinMstMulSelSearch" title="商品複数検索" width="750" height="850" init="0" page="100" description="商品コード複数選択可">
		<panel width="520" cols="2">
			<message id="" value="大分類コード" order="1" />
			<input id="param4" type="text" maxlength="2" order="2" value="param4" script="onblur='bunruiOnblur(this,1)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="中分類コード" order="3" />
			<input id="param5" type="text" maxlength="4" order="4" value="param5" script="onblur='bunruiOnblur(this,2)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="中小分類コード" order="5" />
			<input id="param6" type="text" maxlength="6" order="6" value="param6" script="onblur='bunruiOnblur(this,3)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="6" alert="最大入力文字数は6文字です。" />
			</input>
			<message order="7" id="" value="商品コード" />
			<input order="8" id="term_syohin_cd" value="term_syohin_cd" type="text" maxlength="14" script="onblur='syohinOnblur(this);'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="13" alert="最大入力文字数は14文字です。" />
			</input>
			<message order="9" id="" value="商品名" />
			<input order="10" id="term_syohin_na" value="term_syohin_na" type="text" maxlength="20">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="11" />
			<input id="param2" type="text" maxlength="8" order="12" value="param2">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message order="13" id="notice1" value="商品コードまたは商品名の一部を入力して検索ボタンを押してください。" style="text-align:left;" />
			<message order="14" id="notice2" value="商品コード、商品名を入力した場合はあいまい検索となります。" style="text-align:left;" />
			<message order="15" id="notice3" value="商品数が多い場合、検索及び改ページに時間がかかることがあります。" style="text-align:left;" />
			<input order="16" id="search_button" value="検索" type="button" script="doSearch()" style="text-align:center;" />
			<input id="param0" type="hidden" value="param0" order="17" source="" />
			<input id="param1" type="hidden" value="param1" order="18" source="" />
			<input id="param3" type="hidden" value="param3" order="19" source="" />
		</panel>
		<list width="650" cols="3">
			<column order="1" name="Check"		 mapping="KEY_NO"  label="" width="40" style="text-align:center;" />
			<column order="2" name="yuko_dt"	 mapping="YUKO_DT" label="有効開始日" width="100" style="height:28;text-align:left;padding:0 4px 0 4px" />
			<column order="3" name="syohin_cd"	 mapping="SYOHIN_CD" label="商品コード" width="180" style="height:28;text-align:left;padding:0 5px 0 4px" />
			<column order="4" name="hinmei_kanji_na" mapping="SYOHIN_KANJI_NA" label="商品名" width="300px" style="text-align:left;padding:0 5px 0 4px" />
		</list>
		<sql>
			<id>SyohinSearchQuery</id>
			<body>
				SELECT
					*
				FROM
					(
						SELECT
							'2' AS KEY_NO,
							YUKO_DT,
							SYOHIN_CD,
							RTRIM (CONCAT (SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA)) AS SYOHIN_KANJI_NA,
							BUNRUI1_CD,
							BUNRUI2_CD,
							BUNRUI3_CD
						FROM
							R_SYOHIN
						WHERE
							SHIIRE_HANBAI_KB = '4'
					) AS SYOHIN
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MULTI_SEL_SYOHIN'
			</body>
			<where order="1" bind="param2">YUKO_DT>=:param2</where>
			<where order="2" bind="param4">TRIM(BUNRUI1_CD)=:param4</where>
			<where order="3" bind="param5">TRIM(BUNRUI2_CD)=:param5</where>
			<where order="4" bind="param6">TRIM(BUNRUI3_CD)=:param6</where>
			<where order="5" bind="term_syohin_cd">SYOHIN_CD LIKE %:term_syohin_cd%</where>
			<where order="6" bind="term_syohin_na">SYOHIN_KANJI_NA LIKE %:term_syohin_na%</where>
			<orderby>YUKO_DT, SYOHIN_CD, SYOHIN_KANJI_NA</orderby>
		</sql>
	</popup>
	<popup id="tenpokaisoSearch" title="エリア検索" width="750" height="650" init="1" page="0" description="エリア検索">
		<panel width="520" cols="1">
			<message id="" value="エリアコード" order="1" />
			<input id="term_tenpokaiso_cd" type="text" maxlength="4" order="2" value="term_tenpokaiso_cd">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="エリア名" order="3" />
			<input id="term_tenpokaiso_na" type="text" maxlength="20" order="4" value="term_tenpokaiso_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="エリアコード、エリア名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="area_cd" mapping="AREA_CD" label="エリアコード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="area_kanji_na" mapping="AREA_KANJI_NA" label="エリア名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>AreaSearchQuery</id>
			<body>
				SELECT
					AREA_CD, AREA_KANJI_NA
				FROM R_AREA
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'TENPO'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="term_tenpokaiso_cd">AREA_CD LIKE %:term_tenpokaiso_cd%</where>
			<where order="3" bind="term_tenpokaiso_na">AREA_KANJI_NA LIKE %:term_tenpokaiso_na%</where>
			<orderby>AREA_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="torihikisakiSearch" title="取引先検索" width="750" height="750" init="1" page="50" description="取引先検索">
		<panel width="560" cols="2">
			<message id="" value="取引先コード" order="1"  />
			<input id="term_torihikisaki_cd" type="text" maxlength="5" order="2" value="term_torihikisaki_cd" >
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="5" alert="最大入力文字数は5文字です。" />
			</input>
			<message id="" value="取引先名" order="3"  />
			<input id="term_torihikisaki_na" type="text" maxlength="20" order="4" value="term_torihikisaki_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="取引先コード、取引先名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;"/>
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="torihikisaki_cd" mapping="SHIIRESAKI_CD" label="取引先コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 3px" />
			<column name="tohihikisaki_kanji_na" mapping="SHIIRESAKI_KANJI_NA" label="取引先名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>TorihikisakiSearchQuery</id>
			<body>
				SELECT
					SHIIRESAKI_CD, SHIIRESAKI_KANJI_NA
				FROM R_SHIIRESAKI
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'TORIHIKISAKI'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="term_torihikisaki_cd">SHIIRESAKI_CD LIKE %:term_torihikisaki_cd%</where>
			<where order="3" bind="term_torihikisaki_na">SHIIRESAKI_KANJI_NA LIKE %:term_torihikisaki_na%</where>
			<orderby>SHIIRESAKI_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="tenpoSearch" title="店舗検索" width="750" height="650" init="1" page="0" description="店舗検索">
		<panel width="520" cols="1">
			<message id="" value="店舗コード" order="1" />
			<input id="term_tenpo_cd" type="text" maxlength="4" order="2" value="term_tenpo_cd">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="店舗名" order="3" />
			<input id="term_tenpo_na" type="text" maxlength="20" order="4" value="term_tenpo_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="店舗コード、店舗名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="tenpo_cd" mapping="TENPO_CD" label="店舗コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="店舗名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>TenpoSearchQuery</id>
			<body>
				SELECT
					TENPO_CD, TENPO_KANJI_NA
				FROM R_TENPO
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'TENPO'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="term_tenpo_cd">TENPO_CD LIKE %:term_tenpo_cd%</where>
			<where order="3" bind="term_tenpo_na">TENPO_KANJI_NA LIKE %:term_tenpo_na%</where>
			<orderby>TENPO_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="tenpoCopySearch" title="店舗検索" width="750" height="650" init="1" page="0" description="店舗検索">
		<panel width="520" cols="1">
			<message id="" value="店舗コード" order="1" />
			<input id="term_tenpo_cd" type="text" maxlength="4" order="2" value="term_tenpo_cd">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="店舗名" order="3" />
			<input id="term_tenpo_na" type="text" maxlength="20" order="4" value="term_tenpo_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="店舗コード、店舗名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="tenpo_cd" mapping="TENPO_CD" label="店舗コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="店舗名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>TenpoSearchQuery</id>
			<body>
				SELECT
					TENPO_CD, TENPO_KANJI_NA
				FROM R_TENPO
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'TENPO_OUT'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="term_tenpo_cd">TENPO_CD LIKE %:term_tenpo_cd%</where>
			<where order="3" bind="term_tenpo_na">TENPO_KANJI_NA LIKE %:term_tenpo_na%</where>
			<orderby>TENPO_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="tenpoJisekiSearchOut" title="店舗検索" width="750" height="650" init="1" page="0" description="店舗検索">
		<panel width="520" cols="1">
			<message id="" value="店舗コード" order="1" />
			<input id="term_tenpo_cd" type="text" maxlength="4" order="2" value="term_tenpo_cd">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="店舗名" order="3" />
			<input id="term_tenpo_na" type="text" maxlength="20" order="4" value="term_tenpo_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="店舗コード、店舗名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;">s
			</input>
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="tenpo_cd" mapping="TENPO_CD" label="店舗コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="店舗名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>TenpoSearchQuery</id>
			<body>
				SELECT
					TENPO_CD, TENPO_KANJI_NA
				FROM R_TENPO
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'TENPO_JISEKI_OUT'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="term_tenpo_cd">TENPO_CD LIKE %:term_tenpo_cd%</where>
			<where order="3" bind="term_tenpo_na">TENPO_KANJI_NA LIKE %:term_tenpo_na%</where>
			<orderby>TENPO_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="tenGroupSearch" title="店グループ検索" width="750" height="650" init="1" page="0" description="店グループ検索">
		<panel cols="1">
			<message id="" value="有効日" order="1" style="" />
			<input id="param2" type="text" maxlength="8" order="2" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<input id="param0" type="hidden" value="param0" order="3" source=""/>
			<input id="param1" type="hidden" value="param1" order="4" source=""/>
			<input id="param3" type="hidden" value="param3" order="5" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="groupno_cd" mapping="GROUPNO_CD" label="店グループコード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="name_na" mapping="TEN_GROUP_NA" label="店グループ名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>TengroupSearchQuery</id>
			<body>
				SELECT
					GROUPNO_CD, TEN_GROUP_NA
				FROM R_TENGROUPNO
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'TENGROUP'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="">YOTO_KB=(SELECT TRIM(PARAMETER_TX) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='TNP_TENGROUP_YOTO_KB_DEFAULT')</where>
			<where order="3" bind="">BUNRUI_KB=(SELECT TRIM(PARAMETER_TX) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='KEY_BUNRUI_TENGROUP')</where>
			<where order="4" bind="">TRIM(BUNRUI_CD) =(SELECT TRIM(PARAMETER_TX) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='TNP_TENGROUP_BUNRUI_CD_DEFAULT')</where>
			<orderby>GROUPNO_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="themeSearch" title="テーマ検索" width="750" height="750" init="1" page="100" description="テーマ店舗検索">
		<panel width="600" cols="2">
			<message id="" value="テーマ名" order="1" />
			<input id="term_theme_na" type="text" maxlength="80" order="2" value="term_theme_na" style="">
				<validate id="maxLength" value="80" alert="最大入力文字数は80文字です。" />
			</input>
			<message id="" value="特売区分" order="3" />
			<radio id="term_tokubai_kb" order="4">
				<button label="全て" order="1" value="" checked="true" />
				<button label="チラシ" order="2" value="1" />
				<button label="期間特売" order="3" value="2" />
			</radio>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<input id="search_button" type="button" order="7" value="検索" script="doSearch()" style="text-align:center;"/>
			<input id="param0" type="hidden" value="param0" order="8" source=""/>
			<input id="param1" type="hidden" value="param1" order="9" source=""/>
			<input id="param3" type="hidden" value="param3" order="10" source=""/>
			<input id="param4" type="hidden" value="param4" order="11" source=""/>
		</panel>
		<list width="700" cols="4">
			<column name="theme_cd" mapping="THEME_CD" label="テーマコード" width="110" order="1" style="height:28;text-align:center;padding: 0 5px" />
			<column name="theme_nm" mapping="THEME_NA" label="テーマ名" width="250" order="2" style="text-align:left;padding:0 4px 0 5px;" />
			<column name="hanbai" mapping="HANBAI_START_DT_HANBAI_END_DT" label="販売期間" width="230" order="3" style="text-align:center;" />
			<column name="Select" label="" width="70" order="4" style="text-align:center;" />
		</list>
		<sql>
			<id>ThemeSearchQuery</id>
			<body>
				SELECT
					RT.THEME_CD,
					RTH.THEME_NA,
					CASE
						WHEN  RTH.THEME_HANBAI_START_DT IS NULL THEN '　'
						ELSE  CONCAT(SUBSTRING(RTH.THEME_HANBAI_START_DT, 1, 4), '/',  SUBSTRING(RTH.THEME_HANBAI_START_DT, 5, 2), '/', SUBSTRING(RTH.THEME_HANBAI_START_DT, 7, 2))
					END
					+
					CASE
						WHEN  (RTH.THEME_HANBAI_START_DT IS NULL) AND (RTH.THEME_HANBAI_END_DT IS NULL) THEN '　'
						ELSE  '～'
					END
					 +
					CASE
						WHEN  RTH.THEME_HANBAI_END_DT IS NULL THEN '　'
						ELSE  CONCAT(SUBSTRING(RTH.THEME_HANBAI_START_DT, 1, 4), '/',  SUBSTRING(RTH.THEME_HANBAI_START_DT, 5, 2), '/', SUBSTRING(RTH.THEME_HANBAI_START_DT, 7, 2))
					END	  AS HANBAI_START_DT_HANBAI_END_DT,
					CASE
						WHEN  DTT.MIN_HACHU_DT IS NULL THEN '　'
						ELSE CONCAT(SUBSTRING(DTT.MIN_HACHU_DT, 1, 4), '/',  SUBSTRING(DTT.MIN_HACHU_DT, 5, 2), '/', SUBSTRING(DTT.MIN_HACHU_DT, 7, 2))
					END AS MIN_HACHU_DT
				FROM
					(SELECT
						TENPO_CD, THEME_CD
					FROM
						R_TOKUBAI
					GROUP BY
						TENPO_CD, THEME_CD
					) RT
				LEFT OUTER JOIN DT_TENPO_THEME DTT
					ON  DTT.TENPO_CD  = RT.TENPO_CD
					AND DTT.THEME_CD = RT.THEME_CD
				LEFT OUTER JOIN R_THEME RTH
					ON RTH.THEME_CD = RT.THEME_CD
				LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
					ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'THEME'
			</body>
			<where order="1" bind="param4">TRIM(RT.TENPO_CD) = :param4</where>
			<where order="2" bind="term_theme_na">RTH.THEME_NA LIKE %:term_theme_na%</where>
			<where order="3" bind="param2"> :param2 BETWEEN RTH.THEME_HANBAI_START_DT AND RTH.THEME_HANBAI_END_DT </where>
			<where order="4" bind="term_tokubai_kb">
				EXISTS (
					SELECT
						1
					 FROM
					 	 R_TOKUBAI RT2
					 WHERE
						 RT2.TENPO_CD = RT.TENPO_CD
					 AND RT2.THEME_CD = RT.THEME_CD
					 AND RT2.TOKUBAI_KB = :term_tokubai_kb
				)
			</where>
			<orderby>
				CASE WHEN ISNULL(DTT.MIN_HACHU_DT, '') = '' THEN 1 ELSE 0 END ASC,
					DTT.MIN_HACHU_DT ASC,
				CASE WHEN ISNULL(RTH.THEME_HANBAI_START_DT, '') = '' THEN 1 ELSE 0 END ASC,
					RTH.THEME_HANBAI_START_DT ASC,
				CASE WHEN ISNULL(RTH.THEME_HANBAI_END_DT, '') = '' THEN 1 ELSE 0 END ASC,
					RTH.THEME_HANBAI_END_DT ASC,
				CASE WHEN ISNULL(RTH.THEME_SAKUSEISYA_KB, '') = '' THEN 1 ELSE 0 END ASC,
					RTH.THEME_SAKUSEISYA_KB ASC
			</orderby>
		</sql>
	</popup>
	<popup id="tananbSearch" title="棚番検索" width="750" height="650" init="1" page="100" description="棚番検索">
		<panel width="520" cols="1">
			<message id="" value="*店舗コード" order="1"  />
			<input id="param4" type="text" maxlength="4" order="2" value="param4"  readonly="true" source="">
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="有効日" order="3" style="" />
			<input id="param2" type="text" maxlength="8" order="4" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<input id="param0" type="hidden" value="param0" order="5" source=""/>
			<input id="param1" type="hidden" value="param1" order="6" source=""/>
			<input id="param3" type="hidden" value="param3" order="7" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="tana_nb" mapping="TANA_NB" label="棚番" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="tana_na" mapping="TANA_NA" label="棚番名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>TananbSearchQuery</id>
			<body>
				SELECT
					RG.GONDOLA_NB AS TANA_NB,
					RG.GONDOLA_NA AS TANA_NA
				FROM
					R_GONDOLA RG
				LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
					ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'TANA_NB'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="param4">RG.TENPO_CD=LEFT(CONCAT(:param4 , '          ') , 10)</where>
			<orderby>RG.BUNRUI_CD, RG.GONDOLA_NB</orderby>
		</sql>
	</popup>
	<popup id="hachuSearch" title="発注No検索" width="750" height="650" init="1" page="0" description="発注No検索">
		<panel width="520" cols="2">
			<input id="param0" type="hidden" value="param0" order="1" source=""/>
			<message id="" value="発注No" order="2" />
			<input id="term_hachu_no" type="text" maxlength="13" order="3" value="term_hachu_no">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="13" alert="最大入力文字数は13文字です。" />
			</input>
			<message id="" value="発注名" order="4" />
			<input id="term_hachu_na" type="text" maxlength="20" order="5" value="term_hachu_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="6" style="" />
			<input id="param2" type="text" maxlength="8" order="7" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="8" value="発注No、発注名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="9" value="検索" script="doSearch()" style="text-align:center;"/>
			<input id="param0" type="hidden" value="param0" order="10" source=""/>
			<input id="param1" type="hidden" value="param1" order="11" source=""/>
			<input id="param3" type="hidden" value="param3" order="12" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="hachusyo_nb" mapping="HACHU_NO" label="発注No" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="hachusyo_na" mapping="HACHU_NA" label="発注名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>HachuSyoNbSearchQuery</id>
			<body>
				SELECT
					HACHU_NO,HACHU_NA
				FROM DT_HONBU_HACHU_HEADER
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'HACHU'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="term_hachu_no">HACHU_NO LIKE %:term_hachu_no%</where>
			<where order="3" bind="term_hachu_na">HACHU_NA LIKE %:term_hachu_na%</where>
			<orderby>HACHU_NO ASC</orderby>
		</sql>
	</popup>
	<popup id="userSearch" title="ユーザID検索" width="750" height="750" init="1" page="100" description="ユーザID検索">
		<panel width="520" cols="2">
			<message id="" value="ユーザID" order="1" />
			<input id="term_user_id" type="text" maxlength="20" order="2" value="term_user_id">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="ユーザ名" order="3" />
			<input id="term_user_na" type="text" maxlength="20" order="4" value="term_user_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="ユーザID、ユーザ名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
		</panel>
		<list width="680" cols="3">
			<column name="by_no" mapping="BY_NO" label="ユーザID" width="200" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="by_na" mapping="BY_NA" label="ユーザ名" width="370" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>BuyerSearchQuery</id>
			<body>
				SELECT
					RPU1.USER_ID AS BY_NO,
					RPU1.USER_NA AS BY_NA
				FROM
					R_PORTAL_USER RPU1
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'USER'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN RPU1.YUKO_DT AND RPU1.YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="term_user_id">RPU1.USER_ID LIKE %:term_user_id%</where>
			<where order="4" bind="term_user_na">
					RPU1.USER_NA LIKE %:term_user_na%
				GROUP BY  RPU1.USER_ID, RPU1.USER_NA, RPU1.YUKO_DT
			</where>
			<orderby>RPU1.USER_ID ASC</orderby>
		</sql>
	</popup>
	<popup id="mstTenpoSearch" title="店舗検索" width="750" height="650" init="1" page="0" description="店舗検索">
		<panel width="520" cols="1">
			<message id="" value="店舗コード" order="1" />
			<input id="term_tenpo_cd" type="text" maxlength="4" order="2" value="term_tenpo_cd">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="店舗名" order="3" />
			<input id="term_tenpo_na" type="text" maxlength="20" order="4" value="term_tenpo_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="店舗コード、店舗名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;" />
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="tenpo_cd" mapping="TENPO_CD" label="店舗コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="店舗名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>MstTenpoSearchQuery</id>
			<body>
				SELECT
					TENPO_CD AS TENPO_CD, KANJI_NA AS TENPO_KANJI_NA
				FROM MST_R_TENPO
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MST_TENPO'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN YUKO_DT AND YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="term_tenpo_cd">TENPO_CD LIKE %:term_tenpo_cd%</where>
			<where order="4" bind="term_tenpo_na">KANJI_NA LIKE %:term_tenpo_na%</where>
			<where order="5" bind="">DELETE_FG = '0'</where>
			<orderby>TENPO_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="bunrui1MstSearch" title="大分類検索" width="750" height="650" init="1" page="0" description="大分類検索">
		<panel width="300" cols="2">
			<message id="" value="大分類名" order="1" />
			<input id="term_bunrui1_na" type="text" maxlength="20" order="2" value="term_bunrui1_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="3" style="" />
			<input id="param2" type="text" maxlength="8" order="4" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="5" value="大分類名はあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="6" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="7" source=""/>
			<input id="param1" type="hidden" value="param1" order="8" source=""/>
			<input id="param3" type="hidden" value="param3" order="9" source=""/>
			<input id="param4" type="hidden" value="param4" order="10" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="bunrui1_cd" mapping="BUNRUI1_CD" label="大分類コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui1_kanji_na" mapping="BUNRUI1_KANJI_NA" label="大分類名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui1SearchQuery</id>
			<body>
				SELECT DISTINCT
					RST.BUNRUI1_CD,
					RB1.BUNRUI1_KANJI_NA
				FROM
					MST_VW_SYOHIN_TAIKEI RST
					INNER JOIN (
						SELECT
							R.BUNRUI1_CD,
							R.BUNRUI1_KANJI_NA,
							R.YUKO_DT,
							R.YUKO_END_DT
						FROM
							MST_R_BUNRUI1 R
						WHERE
							R.DELETE_FG = '0'
					) RB1 ON RST.BUNRUI1_CD = RB1.BUNRUI1_CD
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MST_BUNRUI1'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN RB1.YUKO_DT AND RB1.YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="param4">RST.SYSTEM_KB=:param4</where>
			<where order="3" bind="term_bunrui1_na">RB1.BUNRUI1_KANJI_NA LIKE %:term_bunrui1_na%</where>
			<orderby>RST.BUNRUI1_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="bunrui2MstSearch" title="中分類検索" width="750" height="650" init="1" page="0" description="中分類検索">
		<panel width="520" cols="2">
			<message id="" value="*大分類コード" order="3" />
			<input id="param5" type="text" maxlength="2" order="4" value="param5" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="中分類名" order="5" />
			<input id="term_bunrui2_na" type="text" maxlength="20" order="6" value="term_bunrui2_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="7" style="" />
			<input id="param2" type="text" maxlength="8" order="8" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="9" value="中分類名はあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="10" value="検索" script="doSearch()" style="text-align:center;"/>
			<input id="param0" type="hidden" value="param0" order="11" source=""/>
			<input id="param1" type="hidden" value="param1" order="12" source=""/>
			<input id="param3" type="hidden" value="param3" order="13" source=""/>
			<input id="param4" type="hidden" value="param4" order="14" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="bunrui2_cd" mapping="BUNRUI2_CD" label="中分類コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui2_kanji_na" mapping="BUNRUI2_KANJI_NA" label="中分類名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui2SearchQuery</id>
			<body>
				SELECT DISTINCT
					  RST.BUNRUI2_CD,
					 RB2.BUNRUI2_KANJI_NA
				FROM
					MST_VW_SYOHIN_TAIKEI RST
				INNER JOIN (
					SELECT
						  R.BUNRUI2_CD,
						  R.BUNRUI2_KANJI_NA,
						  R.YUKO_DT,
						  R.YUKO_END_DT
					FROM
						MST_R_BUNRUI2 R
					WHERE
						R.DELETE_FG = '0'
				) RB2
					ON RST.BUNRUI2_CD = RB2.BUNRUI2_CD
				LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
					ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MST_BUNRUI2'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN RB2.YUKO_DT AND RB2.YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="param4">RST.SYSTEM_KB=:param4</where>
			<where order="4" bind="param5">RST.BUNRUI1_CD=:param5</where>
			<where order="5" bind="term_bunrui2_na">RB2.BUNRUI2_KANJI_NA LIKE %:term_bunrui2_na%</where>
			<orderby>RST.BUNRUI2_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="bunrui3MstSearch" title="中小分類検索" width="750" height="700" init="1" page="0" description="中小分類検索">
		<panel width="520" cols="2">
			<message id="" value="*大分類コード" order="1" />
			<input id="param5" type="text" maxlength="2" order="2" value="param5" style="" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="*中分類コード" order="3" />
			<input id="param6" type="text" maxlength="4" order="4" value="param6" style="" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="中小分類名" order="5" />
			<input id="term_bunrui3_na" type="text" maxlength="20" order="6" value="term_bunrui3_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="7" style="" />
			<input id="param2" type="text" maxlength="8" order="8" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="9" value="中小分類名はあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="10" value="検索" script="doSearch()" style="text-align:center;" />
			<input id="param0" type="hidden" value="param0" order="11" source=""/>
			<input id="param1" type="hidden" value="param1" order="12" source=""/>
			<input id="param3" type="hidden" value="param3" order="13" source=""/>
			<input id="param4" type="hidden" value="param4" order="14" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="bunrui3_cd" mapping="BUNRUI3_CD" label="中小分類コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui3_kanji_na" mapping="BUNRUI3_KANJI_NA" label="中小分類名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui3SearchQuery</id>
			<body>
				SELECT DISTINCT
					  RST.BUNRUI3_CD,
					 RB3.BUNRUI3_KANJI_NA
				FROM
					MST_VW_SYOHIN_TAIKEI RST
				INNER JOIN (
					SELECT
						  R.BUNRUI3_CD,
						  R.BUNRUI3_KANJI_NA,
						  R.YUKO_DT,
						  R.YUKO_END_DT
					FROM
						MST_R_BUNRUI3 R
					WHERE
						R.DELETE_FG = '0'
				) RB3
					ON RST.BUNRUI3_CD = RB3.BUNRUI3_CD
				LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
					ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MST_BUNRUI3'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN RB3.YUKO_DT AND RB3.YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="param4">RST.SYSTEM_KB=:param4</where>
			<where order="4" bind="param5">RST.BUNRUI1_CD=:param5</where>
			<where order="5" bind="param6">RST.BUNRUI2_CD=:param6</where>
			<where order="6" bind="term_bunrui3_na">RB3.BUNRUI3_KANJI_NA LIKE %:term_bunrui3_na%</where>
			<orderby>RST.BUNRUI3_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="bunrui5MstSearch" title="小分類検索" width="750" height="700" init="1" page="0" description="小分類検索">
		<panel width="520" cols="2">
			<message id="" value="*大分類コード" order="1" />
			<input id="param5" type="text" maxlength="2" order="2" value="param5" style=" background-color:#e0e0e0; color:#383838;" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="*中分類コード" order="3" />
			<input id="param6" type="text" maxlength="4" order="4" value="param6" style="background-color:#e0e0e0; color:#383838;" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="*中小分類コード" order="5" />
			<input id="param7" type="text" maxlength="6" order="6" value="param7" style="background-color:#e0e0e0; color:#383838;" readonly="true" source="">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="6" alert="最大入力文字数は6文字です。" />
			</input>
			<message id="" value="小分類名" order="7" />
			<input id="term_bunrui5_na" type="text" maxlength="20" order="8" value="term_bunrui5_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="9" style="" />
			<input id="param2" type="text" maxlength="8" order="10" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="11" value="小分類名はあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="12" value="検索" script="doSearch()" style="text-align:center;" />
			<input id="param0" type="hidden" value="param0" order="13" source="" />
			<input id="param1" type="hidden" value="param1" order="14" source="" />
			<input id="param3" type="hidden" value="param3" order="15" source="" />
			<input id="param4" type="hidden" value="param4" order="16" source="" />
		</panel>
x
		<list width="500" cols="3">
			<column name="bunrui5_cd" mapping="BUNRUI5_CD" label="小分類コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="bunrui5_kanji_na" mapping="BUNRUI5_KANJI_NA" label="小分類名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>Bunrui5SearchQuery</id>
			<body>
				SELECT DISTINCT
					RST.BUNRUI5_CD,
					RB5.BUNRUI5_KANJI_NA
				FROM
					MST_VW_SYOHIN_TAIKEI RST
				INNER JOIN (
					SELECT
						  R.BUNRUI5_CD,
						  R.BUNRUI5_KANJI_NA,
						  R.YUKO_DT,
						  R.YUKO_END_DT
					FROM
						MST_R_BUNRUI5 R
					WHERE
						R.DELETE_FG = '0'
				) RB5
					ON RST.BUNRUI5_CD = RB5.BUNRUI5_CD
				LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
					ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MST_BUNRUI5'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN RB5.YUKO_DT AND RB5.YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="param4">RST.SYSTEM_KB=:param4</where>
			<where order="4" bind="param5">RST.BUNRUI1_CD=:param5</where>
			<where order="5" bind="param6">RST.BUNRUI2_CD=:param6</where>
			<where order="6" bind="param7">RST.BUNRUI3_CD=:param7</where>
			<where order="7" bind="term_bunrui5_na">RB5.BUNRUI5_KANJI_NA LIKE %:term_bunrui5_na%</where>
			<orderby>RST.BUNRUI5_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="syohinMstSearch" title="商品検索" width="750" height="850" init="0" page="100" description="商品検索(分類1指定)">
		<panel width="520" cols="2">
			<message id="" value="大分類コード" order="1" />
			<input id="param4" type="text" maxlength="2" order="2" value="param4" script="onblur='bunruiOnblur(this,1)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="2" alert="最大入力文字数は2文字です。" />
			</input>
			<message id="" value="中分類コード" order="3" />
			<input id="param5" type="text" maxlength="4" order="4" value="param5" script="onblur='bunruiOnblur(this,2)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="中小分類コード" order="5" />
			<input id="param6" type="text" maxlength="6" order="6" value="param6" script="onblur='bunruiOnblur(this,3)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="6" alert="最大入力文字数は6文字です。" />
			</input>
			<message id="" value="小分類コード" order="7" />
			<input id="param7" type="text" maxlength="9" order="8" value="param7" script="onblur='bunruiOnblur(this,4)'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="9" alert="最大入力文字数は9文字です。" />
			</input>
			<message id="" value="商品コード" order="9" />
			<input id="term_syohin_cd" value="term_syohin_cd" type="text" maxlength="13" order="10" script="onblur='syohinOnblur(this);'">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="13" alert="最大入力文字数は13文字です。" />
			</input>
			<message id="" value="商品名" order="11" />
			<input id="term_syohin_na" value="term_syohin_na" type="text" maxlength="20" order="12">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="13" style="" />
			<input id="param2" type="text" maxlength="8" order="14" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice1" value="商品コードまたは商品名の一部を入力して検索ボタンを押してください。" style="text-align:left;" order="15" />
			<message id="notice2" value="商品コード、商品名を入力した場合はあいまい検索となります。" style="text-align:left;" order="16" />
			<message id="notice3" value="商品数が多い場合、検索及び改ページに時間がかかることがあります。" style="text-align:left;" order="17" />
			<input id="search_button" value="検索" type="button" script="doSearch()" style="text-align:center;" order="18" />
			<input id="param0" type="hidden" value="param0" order="19" source=""/>
			<input id="param1" type="hidden" value="param1" order="20" source=""/>
			<input id="param3" type="hidden" value="param3" order="21" source=""/>
		</panel>
		<list width="620" cols="3">
			<column order="1" name="syohin_cd" mapping="SYOHIN_CD" label="商品コード" width="180" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column order="2" name="hinmei_kanji_na" mapping="HINMEI_KANJI_NA" label="商品名" width="350" style="text-align:left;padding: 0px 5px 0 5px" />
			<column order="3" name="Select" label="" width="70" style="text-align:center;" />
		</list>
		<sql>
			<id>SyohinSearchQuery</id>
			<body>
				SELECT
					  RS.SYOHIN_CD,
					 TRIM(CONCAT(RS.HINMEI_KANJI_NA, '　', RS.KIKAKU_KANJI_NA)) HINMEI_KANJI_NA
				FROM (
					SELECT
						  RS.SYOHIN_CD,
						  RS.YUKO_DT,
						  RS.YUKO_END_DT,
						  RS.HINMEI_KANJI_NA,
						  RS.HINMEI_KANA_NA,
						  ISNULL(RS.KIKAKU_KANJI_NA, RS.KIKAKU_KANA_NA) AS KIKAKU_KANJI_NA,
						  RS.BUNRUI1_CD,
						  RS.BUNRUI2_CD,
						  RS.BUNRUI3_CD,
						  RS.BUNRUI5_CD
					FROM
						  MST_R_SYOHIN RS
					WHERE
						  DELETE_FG = '0'
				  ) RS
				LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
					ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MST_SYOHIN'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN RS.YUKO_DT AND RS.YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID=:param3</where>
			<where order="3" bind="param4">BUNRUI1_CD=:param4</where>
			<where order="4" bind="param5">BUNRUI2_CD=:param5</where>
			<where order="5" bind="param6">BUNRUI3_CD=:param6</where>
			<where order="6" bind="param7">BUNRUI5_CD=:param7</where>
			<where order="7" bind="term_syohin_cd">SYOHIN_CD LIKE %:term_syohin_cd%</where>
			<where order="8" bind="term_syohin_na">
				(
					HINMEI_KANJI_NA LIKE %:term_syohin_na%
					OR HINMEI_KANA_NA LIKE %:term_syohin_na%
					OR KIKAKU_KANJI_NA LIKE %:term_syohin_na%
				)
			</where>
			<orderby>SYOHIN_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="centerSearch" title="センター検索" width="750" height="650" init="1" page="0" description="センター検索">
		<panel width="520" cols="1">
			<message id="" value="センターコード" order="1"  />
			<input id="term_tenpo_cd" type="text" maxlength="4" order="2" value="term_tenpo_cd" >
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="4" alert="最大入力文字数は4文字です。" />
			</input>
			<message id="" value="センター名" order="3"  />
			<input id="term_tenpo_na" type="text" maxlength="20" order="4" value="term_tenpo_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="センターコード、センター名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;">
			</input>
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
			<input id="param4" type="hidden" value="param4" order="12" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="tenpo_cd" mapping="TENPO_CD" label="センターコード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 4px" />
			<column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="センター名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>MstTenpoSearchQuery</id>
			<body>
				SELECT
					TENPO_CD AS TENPO_CD,
					KANJI_NA AS TENPO_KANJI_NA
				FROM
					MST_R_TENPO
				LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
					ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'CENTER'
			</body>
			<where order="1" bind="param2">:param2 BETWEEN YUKO_DT AND YUKO_END_DT</where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="param4">TENPO_KB=:param4</where>
			<where order="4" bind="term_tenpo_cd">TENPO_CD LIKE %:term_tenpo_cd%</where>
			<where order="5" bind="term_tenpo_na">KANJI_NA LIKE %:term_tenpo_na%</where>
			<where order="6" bind="">DELETE_FG = '0'</where>
			<orderby>TENPO_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="mstTengroupSearch" title="店グループ検索" width="750" height="650" init="1" page="0" description="店グループ検索">
		<panel cols="1">
			<message id="" value="有効日" order="1" style="" />
			<input id="param2" type="text" maxlength="8" order="2" value="param2" style="" source="" readonly="true">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<input id="param0" type="hidden" value="param0" order="3" source=""/>
			<input id="param1" type="hidden" value="param1" order="4" source=""/>
			<input id="param3" type="hidden" value="param3" order="5" source=""/>
			<input id="param4" type="hidden" value="param4" order="6" source=""/>
		</panel>
		<list width="520" cols="3">
			<column name="groupno_cd" mapping="GROUPNO_CD" label="店グループコード" order="1" width="140" style="height:28;text-align:left; padding: 0 4px 0 5px" />
			<column name="name_na" mapping="TEN_GROUP_NA" label="店グループ名" order="2" width="290" style="text-align:left;padding: 0 5px" />
			<column name="Select" label="" order="3" width="70" style="text-align:center;" />
		</list>
		<sql>
			<id>TengroupSearchQuery</id>
			<body>
				SELECT
					CONCAT('G', GROUPNO_CD) AS GROUPNO_CD,
					NAME_NA AS TEN_GROUP_NA
				FROM MST_R_TENGROUP_HEADER
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MST_TENGROUP'
			</body>
			<where order="1" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="2" bind="param4">YOTO_KB=:param4</where>
			<where order="3" bind="">DELETE_FG='0'</where>
			<orderby>GROUPNO_CD ASC</orderby>
		</sql>
	</popup>
	<popup id="torihikisakiMstSearch" title="取引先検索" width="750" height="750" init="1" page="50" description="取引先検索">
		<panel width="560" cols="2">
			<message id="" value="取引先コード" order="1" />
			<input id="term_torihikisaki_cd" type="text" maxlength="5" order="2" value="term_torihikisaki_cd">
				<validate id="isNum" value="" alert="英文字または数字のみ入力できます。" />
				<validate id="maxLength" value="5" alert="最大入力文字数は5文字です。" />
			</input>
			<message id="" value="取引先名" order="3" />
			<input id="term_torihikisaki_na" type="text" maxlength="20" order="4" value="term_torihikisaki_na">
				<validate id="maxLength" value="20" alert="最大入力文字数は20文字です。" />
			</input>
			<message id="" value="有効日" order="5" style="" />
			<input id="param2" type="text" maxlength="8" order="6" value="param2" style="" source="">
				<validate id="isDate" value="" alert="日付形式ではありません。" />
			</input>
			<message id="notice" order="7" value="取引先コード、取引先名ともにあいまい検索となります。" style="text-align:left;" />
			<input id="search_button" type="button" order="8" value="検索" script="doSearch()" style="text-align:center;" />
			<input id="param0" type="hidden" value="param0" order="9" source=""/>
			<input id="param1" type="hidden" value="param1" order="10" source=""/>
			<input id="param3" type="hidden" value="param3" order="11" source=""/>
		</panel>
		<list width="500" cols="3">
			<column name="torihikisaki_cd" mapping="SHIIRESAKI_CD" label="取引先コード" width="140" order="1" style="height:28;text-align:left;padding: 0px 5px 0 3px" />
			<column name="tohihikisaki_kanji_na" mapping="SHIIRESAKI_KANJI_NA" label="取引先名" width="270" order="2" style="text-align:left;padding: 0px 5px 0 5px" />
			<column name="Select" label="" width="70" order="3" style="text-align:center;" />
		</list>
		<sql>
			<id>TorihikisakiSearchQuery</id>
			<body>
				SELECT
					SHIIRESAKI_CD AS SHIIRESAKI_CD,
					SHIIRESAKI_KANJI_NA AS SHIIRESAKI_KANJI_NA
				FROM (
					SELECT
						TORIHIKISAKI_CD AS SHIIRESAKI_CD,
						TORIHIKISAKI_KANJI_NA AS SHIIRESAKI_KANJI_NA,
						RT.TEKIYO_START_DT as TEKIYO_START_DT
					FROM
						MST_R_TORIHIKISAKI RT
					WHERE
						RT.CHOAI_KB = '1'
					AND RT.TORIKESHI_FG = '0'
					AND RT.TEKIYO_START_DT = (
						SELECT
							MAX(SUB.TEKIYO_START_DT)
						FROM
							MST_R_TORIHIKISAKI SUB
						WHERE
							SUB.COMP_CD = RT.COMP_CD
							AND SUB.CHOAI_KB = RT.CHOAI_KB
							AND SUB.TORIHIKISAKI_CD = RT.TORIHIKISAKI_CD
							AND SUB.TORIKESHI_FG = '0'
							AND SUB.TORIHIKI_TEISHI_KB = '0'
					)
				) RT
					LEFT OUTER JOIN R_SUBSCREEN_KANRI RSK
						ON RSK.SUBSYSTEM_ID = 'AUTOORDERCOMMON' AND RSK.SUB_SCREEN_ID = 'MST_TORIHIKISAKI'
			</body>
			<where order="1" bind="param2"><![CDATA[ RT.TEKIYO_START_DT <= :param2 ]]></where>
			<where order="2" bind="param3">RSK.KINO_ID = :param3</where>
			<where order="3" bind="term_torihikisaki_cd">SHIIRESAKI_CD LIKE %:term_torihikisaki_cd%</where>
			<where order="4" bind="term_torihikisaki_na">SHIIRESAKI_KANJI_NA LIKE %:term_torihikisaki_na%</where>
			<orderby>SHIIRESAKI_CD ASC</orderby>
		</sql>
	</popup>
</config>